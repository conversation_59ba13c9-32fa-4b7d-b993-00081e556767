import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpResponse
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { AuthService } from 'src/app/auth/services';
import { ServerMessage } from '../models';
import { AppToasterService, CommonService } from '../services';

@Injectable({ providedIn: 'root' })
export class ServerErrorInterceptor implements HttpInterceptor {
  constructor(
    private readonly authService: AuthService,
    private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    this.commonService.isApiCallInProgress$.next(true);
    return next.handle(request).pipe(
      tap((event) => {
        if (event instanceof HttpResponse) {
          this.commonService.isApiCallInProgress$.next(false);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.commonService.isApiCallInProgress$.next(false);

        // Add iOS-specific debugging
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
                     (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);

        if (isIOS) {
          console.log('iOS - HTTP Error in interceptor:', {
            status: error.status,
            statusText: error.statusText,
            url: error.url,
            message: error.message,
            error: error.error
          });
        }

        const errorResponse: ServerMessage = error.error?.error;
        if (errorResponse?.message) {
          if (401 === error.status) {
            this.toasterService.error(errorResponse.message);
            this.authService.logOut();
            return throwError(() => error);
          } else {
            this.toasterService.error(errorResponse.message);
            return throwError(() => error);
          }
        } else {
          // Add more specific error message for iOS debugging
          const errorMsg = isIOS ?
            `Something went wrong! Status: ${error.status}, URL: ${error.url}` :
            'Something went wrong!';
          this.toasterService.error(errorMsg);
          return throwError(() => error);
        }
      })
    );
  }
}
