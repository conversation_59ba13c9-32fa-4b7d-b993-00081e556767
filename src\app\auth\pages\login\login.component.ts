import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { MenuConfigService, NavigationService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { GoogleParams, LoginFormGroupType, LoginResponse, ThirdPartyLoginParams } from '../../models';
import { AuthService } from '../../services';
import { AuthCommonLayoutComponent } from '../auth-common-layout/auth-common-layout.component';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { GoogleLoginProvider, GoogleSigninButtonModule, SocialAuthService } from '@abacritt/angularx-social-login';


const DEPENDENCIES = {
  COMPONENTS: [AuthCommonLayoutComponent],
  MODULES: [
    ReactiveFormsModule,
    MatInputModule,
    SharedModule,
    RouterModule,
    DirectivesModule,
    MatFormFieldModule,
    MatIconModule,
    MatButtonModule,
    MatCheckboxModule,
    GoogleSigninButtonModule
  ]
};
@Component({
  standalone: true,
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.MODULES],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginComponent extends BaseComponent implements OnInit {
  loginFormGroup!: FormGroup<LoginFormGroupType>;
  hide = true;
  referedBy!: string;
  referralFromEmail!: string;

  constructor(
    private readonly authService: AuthService,
    private readonly navigationService: NavigationService,
    private readonly router: Router,
    private readonly socialAuthService: SocialAuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly menuConfigService: MenuConfigService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.socialAuthService.authState.pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.socialAuthService.getAccessToken(GoogleLoginProvider.PROVIDER_ID).then(accessToken => {
          this.loginWithThirdParty(user as any, accessToken);
        });
      }
    });
  }

  initializeForm(): void {
    this.loginFormGroup = new FormGroup<LoginFormGroupType>({
      userNameOrEmailAddress: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      password: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      rememberClient: new FormControl(false, {
        nonNullable: true
      })
    });
  }

  onSubmit(): void {
    if (this.loginFormGroup.invalid) {
      this.loginFormGroup.markAllAsTouched();
      return;
    }

    // Add iOS debugging
    if (this.isIOS()) {
      console.log('iOS detected - Starting login process');
      console.log('Form data:', this.loginFormGroup.getRawValue());
    }

    this.showBtnLoader = true;

    this.loginFormGroup.markAsUntouched();

    // Add iOS debugging for the request
    if (this.isIOS()) {
      console.log('iOS - About to make login request');
      console.log('iOS - API URL will be constructed from environment');
    }

    this.authService
      .login(this.loginFormGroup.getRawValue())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: LoginResponse) => {
          const isNotAnUser = [
            this.constants.roleIds.ADMIN,
            this.constants.roleIds.INSTRUCTOR,
            this.constants.roleIds.SUPERVISOR,
            this.constants.roleIds.DESK_MANAGER
          ].includes(res.result.roleId);
          this.showBtnLoader = false;
          this.menuConfigService.resetMenuConfig();
          if (res.result.referralFirstName) {
            this.referedBy = `${res.result.referralFirstName} ${res.result.referralLastName}`;
            this.referralFromEmail = res.result.referralEmail;
          }
          this.redirectionAfterLogin(isNotAnUser || res.result.isProfileCompleted);
          this.cdr.detectChanges();
        },
        error: (error: any) => {
          this.showBtnLoader = false;
          console.error('Login error:', error);

          // Add iOS-specific debugging with alert
          if (this.isIOS()) {
            const errorDetails = {
              status: error?.status,
              message: error?.message,
              error: error?.error,
              url: error?.url,
              statusText: error?.statusText
            };
            console.log('iOS detected - Login error details:', errorDetails);

            // Show alert for iOS debugging
            alert(`iOS Login Error Debug:
Status: ${error?.status || 'Unknown'}
Message: ${error?.message || 'No message'}
Status Text: ${error?.statusText || 'No status text'}
URL: ${error?.url || 'No URL'}
Error: ${JSON.stringify(error?.error || 'No error details')}`);
          }

          this.cdr.detectChanges();
        }
      });
  }

  redirectionAfterLogin(isProfileCompleted: boolean): void {
    if (isProfileCompleted) {
      this.navigationService.navigateToDashboard();
      return;
    }

    const email = this.loginFormGroup.get('userNameOrEmailAddress')?.value;
    const queryParams: any = { isProfileCompleted, email, referralFromEmail: this.referralFromEmail };
    if (this.referedBy) {
      queryParams.referedBy = this.referedBy;
    }
    this.router.navigate([this.path.auth.root, this.path.auth.signUp], {
      queryParams: queryParams
    });
  }

  onForgotPasswordClick(): void {
    const email = this.loginFormGroup.get('userNameOrEmailAddress')?.value;
    this.router.navigate([this.path.root, this.path.auth.root, this.path.auth.forgotPassword.root, this.path.auth.forgotPassword.init], {
      queryParams: { email: email }
    });
  }

  loginWithThirdParty(userInfo: GoogleParams, accessToken: any): void {
    this.authService
      .loginWithThirdParty(this.getThirdPartyLoginParams(userInfo, accessToken))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: LoginResponse) => {
          this.loginFormGroup.get('userNameOrEmailAddress')?.setValue(userInfo.email);
          this.redirectionAfterLogin(res.result.isProfileCompleted);
        },
        error: (error: any) => {
          console.error('Third-party login error:', error);

          // Add iOS-specific debugging
          if (this.isIOS()) {
            console.log('iOS detected - Third-party login error details:', {
              status: error?.status,
              message: error?.message,
              error: error?.error,
              url: error?.url
            });
          }
        }
      });
  }

  getThirdPartyLoginParams(userInfo: GoogleParams, accessToken: any): ThirdPartyLoginParams {
    return {
      authProvider: 'Google',
      providerKey: userInfo.id,
      providerAccessCode: accessToken,
      singleSignIn: false
    };
  }

  private isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  }
}
